# OAuth Branding Setup Guide

This guide will help you configure professional branding for your Google OAuth consent screen so users see "ORA" instead of the ugly backend URL.

## Current Problem

Users see: `"You're signing back in to ora-backend-************.us-central1.run.app"`

## Goal

Users should see: `"You're signing back in to ORA"`

## Step-by-Step Setup

### Step 1: Configure OAuth Consent Screen

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Select project: `ora-phase1`

2. **Navigate to OAuth Consent Screen**
   - Go to "APIs & Services" → "OAuth consent screen"

3. **Configure Application Information**
   ```
   Application name: ORA
   User support email: <EMAIL>
   Application logo: [Upload your ORA logo - optional]
   ```

4. **Set Application Domain Information**
   ```
   Application home page: https://talktoora.com
   Application privacy policy URL: https://talktoora.com/privacy
   Application terms of service URL: https://talktoora.com/terms
   ```

5. **Add Authorized Domains**
   ```
   talktoora.com
   us-central1.run.app
   ```

6. **Developer Contact Information**
   ```
   Email addresses: <EMAIL>
   ```

7. **Click "Save and Continue"**

### Step 2: Update OAuth Client Name

1. **Go to Credentials**
   - Navigate to "APIs & Services" → "Credentials"

2. **Edit OAuth 2.0 Client**
   - Click on your OAuth 2.0 Client ID
   - Current ID: `************-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com`

3. **Update Client Name**
   ```
   Name: ORA Application
   ```

4. **Update Authorized Origins** (add both)
   ```
   https://talktoora.com
   https://ora-frontend-************.us-central1.run.app
   https://ora-backend-************.us-central1.run.app
   ```

5. **Update Authorized Redirect URIs** (add all)
   ```
   https://talktoora.com/auth/callback
   https://talktoora.com/auth/google/callback
   https://ora-frontend-************.us-central1.run.app/auth/callback
   https://ora-frontend-************.us-central1.run.app/auth/google/callback
   https://ora-backend-************.us-central1.run.app/auth/callback
   https://ora-backend-************.us-central1.run.app/auth/google/callback
   ```

6. **Save Changes**

### Step 3: Deploy Updated Application

The privacy and terms pages have been added to your application. Deploy the updates:

```bash
# Deploy with updated routes
gcloud builds submit --config=cloudbuild.yaml
```

### Step 4: Test the Changes

1. **Clear Browser Cache**
   - Clear cookies and cache for your domain
   - Or use incognito/private browsing

2. **Test OAuth Flow**
   - Go to your application
   - Try to sign in with Google
   - You should now see "ORA" instead of the backend URL

### Step 5: Verify Privacy/Terms Pages

After deployment, verify these pages work:
- https://talktoora.com/privacy
- https://talktoora.com/terms
- https://ora-frontend-************.us-central1.run.app/privacy
- https://ora-frontend-************.us-central1.run.app/terms

## Expected Results

### Before
```
"You're signing back in to ora-backend-************.us-central1.run.app"
```

### After
```
"You're signing back in to ORA"
```

## Important Notes

1. **Domain Setup First**: Complete your custom domain setup before updating OAuth URLs
2. **Keep Both Domains**: Maintain both original and custom domain URLs during transition
3. **Propagation Time**: OAuth changes can take 5-10 minutes to take effect
4. **Testing**: Always test in incognito mode to avoid cached OAuth data

## Troubleshooting

### Still Seeing Backend URL
- Clear browser cache completely
- Wait 10 minutes for OAuth changes to propagate
- Verify OAuth consent screen configuration is saved

### Privacy/Terms Pages Not Loading
- Ensure deployment completed successfully
- Check that routes were added correctly
- Verify pages are accessible at both domains

### OAuth Errors
- Double-check all redirect URIs are correctly added
- Ensure authorized domains include both your custom domain and Cloud Run domain
- Verify JavaScript origins are properly configured

## Additional Branding (Optional)

For even more professional appearance:

1. **Add Application Logo**
   - Upload a 120x120px logo in OAuth consent screen
   - Use your ORA brand logo

2. **Custom Domain Email**
   - Set up email forwarding: <EMAIL> → <EMAIL>
   - Update contact emails to use custom domain

3. **Verification Badge**
   - Consider Google verification for production apps
   - Removes "unverified app" warnings

---

**Next Step**: After completing this setup, users will see a clean, professional "ORA" branding instead of the technical backend URL!
